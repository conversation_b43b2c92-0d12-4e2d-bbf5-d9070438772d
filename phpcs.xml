<?xml version="1.0"?>

<ruleset name="wpcom-standard">
	<description>The phpcs standard for wpcom code.</description>

	<!-- Keep up to date with https://opengrok.a8c.com/source/xref/trunk/phpcs.xml -->
	<arg value="ps"/>
	<arg name="parallel" value="20"/>
	<rule ref="NoGetCurrentUser"/>
	<rule ref="VariableAnalysis"/>
	<rule ref="VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable">
		<type>error</type>
	</rule>
	<rule ref="VariableAnalysis.CodeAnalysis.VariableAnalysis">
		<properties>
			<property name="allowUnusedCaughtExceptions" value="true"/>
		</properties>
	</rule>
	<rule ref="WordPress">
		<rule ref="WordPress.WP.GlobalVariablesOverride">
			<properties>
				<property name="custom_test_class_whitelist" type="array">
					<element value="Bkismet_UnitTestCase"/>
				</property>
			</properties>
		</rule>
	</rule>
	<rule ref="WordPress.WP.I18n.NoHtmlWrappedStrings">
		<type>error</type>
	</rule>
	<rule ref="Generic.CodeAnalysis.UnusedFunctionParameter"/>
	<rule ref="Generic.Arrays.DisallowShortArraySyntax">
		<severity>0</severity>
	</rule>
	<rule ref="Universal.Arrays.DisallowShortArraySyntax">
		<severity>0</severity>
	</rule>
	<rule ref="Squiz.Commenting">
		<severity>0</severity>
	</rule>
	<rule ref="Generic.Commenting">
		<severity>0</severity>
	</rule>
	<rule ref="WordPress.Files.FileName">
		<severity>0</severity>
	</rule>
	<rule ref="WordPress.DB.PreparedSQL.NotPrepared">
		<severity>0</severity>
	</rule>
	<rule ref="WordPress.WP.CapitalPDangit">
		<severity>0</severity>
	</rule>
	<rule ref="WordPress.Arrays.ArrayDeclarationSpacing.AssociativeArrayFound">
		<severity>0</severity>
	</rule>
	<!-- end -->

	<!-- nonsense rules -->
	<rule ref="WordPress-Core">
		<exclude name="WordPress.PHP.YodaConditions.NotYoda"/>
		<exclude name="WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned"/>
		<exclude name="Generic.Formatting.MultipleStatementAlignment.NotSameWarning"/>
	</rule>

	<!-- Overrides -->
	<file>classes/</file>
	<file>blocks-everywhere.php</file>

	<exclude-pattern>build/</exclude-pattern>
	<exclude-pattern>dist/</exclude-pattern>
	<exclude-pattern>node_modules/</exclude-pattern>
	<exclude-pattern>*/vendor/</exclude-pattern>
	<exclude-pattern>release/</exclude-pattern>

	<arg name="extensions" value="php" />

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="blocks-everywhere"/>
			</property>
		</properties>
	</rule>
	<rule ref="WordPress.Security.EscapeOutput">
		<properties>
			<property name="customAutoEscapedFunctions" type="array">
				<element value="html_output" />
			</property>
		</properties>
	</rule>

	<!-- Functions we don't want in code that should be separate from wpcom-->
	<rule ref="Generic.PHP.ForbiddenFunctions">
		<properties>
			<property name="forbiddenFunctions" type="array">
				<element key="require_lib" value="null"/>
			</property>
		</properties>
	</rule>
	<!-- end Overrides -->
</ruleset>
