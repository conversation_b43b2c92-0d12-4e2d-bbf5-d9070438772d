<?xml version="1.0"?>
<psalm
    totallyTyped="false"
    errorLevel="3"
    phpVersion="5.6"
    resolveFromConfigFile="true"
    findUnusedVariablesAndParams="true"
    strictBinaryOperands="true"
    ensureArrayStringOffsetsExist="false"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
   	autoloader="bin/psalm.php"
>
    <projectFiles>
        <file name="blocks-everywhere.php" />
        <directory name="classes" />
        <ignoreFiles>
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>
    <globals>
        <var name="wpdb" type="object" />
    </globals>
    <issueHandlers>
    	<TooManyArguments>
            <errorLevel type="suppress">
                <referencedFunction name="apply_filters"/>
            </errorLevel>
        </TooManyArguments>

        <LessSpecificReturnType errorLevel="info" />

        <!-- level 3 issues - slightly lazy code writing, but provably low false-negatives -->

        <DeprecatedMethod errorLevel="info" />
        <DeprecatedProperty errorLevel="info" />
        <DeprecatedClass errorLevel="info" />
        <DeprecatedConstant errorLevel="info" />
        <DeprecatedFunction errorLevel="info" />
        <DeprecatedInterface errorLevel="info" />
        <DeprecatedTrait errorLevel="info" />

        <InternalMethod errorLevel="info" />
        <InternalProperty errorLevel="info" />
        <InternalClass errorLevel="info" />

        <MissingClosureReturnType errorLevel="suppress" />
        <MissingClosureParamType errorLevel="suppress" />

        <MissingReturnType errorLevel="info" />
        <MissingPropertyType errorLevel="info" />
        <InvalidDocblock errorLevel="info" />

        <PropertyNotSetInConstructor errorLevel="info" />
        <MissingConstructor errorLevel="info" />
        <MissingParamType errorLevel="info" />

        <RedundantCondition errorLevel="info" />

        <DocblockTypeContradiction errorLevel="info" />
        <RedundantConditionGivenDocblockType errorLevel="info" />

        <UnresolvableInclude errorLevel="info" />

        <RawObjectIteration errorLevel="info" />

        <InvalidStringClass errorLevel="info" />
    </issueHandlers>
</psalm>
