{"name": "automattic/blocks-everywhere", "description": "Puts <PERSON><PERSON><PERSON> everywhere it can - bbPress, comments, and Buddy<PERSON>ress.", "require": {"php": "7.4.* || 8.*"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "0.7.2", "wp-coding-standards/wpcs": "2.3.0", "sirbrillig/phpcs-variable-analysis": "2.11.3", "sirbrillig/phpcs-no-get-current-user": "1.1.0", "phpcsstandards/phpcsextra": "1.0.0-alpha3", "vimeo/psalm": "4.23.0", "php-stubs/wordpress-stubs": "5.9.3", "phpunit/phpunit": "^9.5"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "composer/package-versions-deprecated": true}}, "scripts": {"lint": "./vendor/bin/phpcs -s", "psalm-check": "./vendor/bin/psalm --show-info=false", "test": "./vendor/bin/phpunit"}}