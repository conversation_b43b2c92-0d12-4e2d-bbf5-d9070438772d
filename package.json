{"name": "blocks-everywhere", "version": "1.23.0", "description": "<PERSON><PERSON><PERSON> in WordPress comments, admin pages, bbPress, and BuddyPress.", "main": "src/index.tsx", "scripts": {"start": "wp-scripts start", "start:sync": "onchange '*.php' readme.txt 'build/**/*' 'classes/**/*.php' -- yarn build:sync", "build": "wp-scripts build && ./bin/lintfix.sh", "build:sync": "rsync -az --delete *.php readme.txt *.png build LICENSE.md classes build $npm_config_blocks_everywhere", "release": "yarn build && rm -rf release && mkdir -p release && cp -R *.php readme.txt *.png build LICENSE.md classes release", "release:sync": "rsync -az --delete release/ $npm_config_blocks_everywhere", "dist": "yarn release && rm -rf dist && mkdir dist && mv release blocks-everywhere && zip blocks-everywhere.zip -r blocks-everywhere && mv blocks-everywhere release && mv blocks-everywhere.zip dist && release-it", "dist:svn": "./bin/svn.sh $npm_package_version", "lint:css": "wp-scripts lint-style", "lint:js": "wp-scripts lint-js", "lint:php": "composer run-script lint", "test:php": "composer run-script test", "update": "ncu -u"}, "author": "Automattic", "license": "GPL-2.0-or-later", "devDependencies": {"@babel/core": "^7.24.3", "@babel/preset-env": "^7.24.3", "@babel/preset-react": "^7.24.1", "@types/eslint": "^8.56.6", "@wordpress/babel-preset-default": "^7.37.0", "@wordpress/prettier-config": "^3.10.0", "@wordpress/scripts": "^27.4.0", "babel-plugin-emotion": "^11.0.0", "babel-plugin-inline-json-import": "^0.3.2", "eslint": "^8.57.0", "eslint-config-wpcalypso": "^6.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-wpcalypso": "^8.0.0", "npm-check-updates": "^16.14.17", "onchange": "^7.1.0", "precss": "^4.0.0", "release-it": "^17.1.1", "remove-files-webpack-plugin": "^1.5.0"}, "dependencies": {"@automattic/color-studio": "^2.5.0", "@automattic/isolated-block-editor": "^2.29.0", "@automattic/typography": "^1.0.0", "use-debounce": "^10.0.0"}, "release-it": {"github": {"release": true, "assets": ["dist/blocks-everywhere.zip"]}, "npm": false}}