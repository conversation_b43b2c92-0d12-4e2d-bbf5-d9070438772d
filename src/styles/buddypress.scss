// BuddyPress & 2021
.gutenberg-support {
	.edit-post-header button:not(:hover):not(:active):not(.has-background):not(.is-primary),
	.block-editor-inserter__panel-content button:not(:hover):not(:active):not(.has-background),
	.block-editor-inserter__tabs button:not(:hover):not(:active):not(.has-background),
	#buddypress.twentytwentyone button {
		background: inherit;
		border: inherit;
		border-radius: inherit;
		color: inherit;
		border-color: inherit !important;
	}

	#whats-new-content {
		min-height: 120px;
	}

	#whats-new-submit input[type="reset"],
	#whats-new {
		display: none;
	}
}
