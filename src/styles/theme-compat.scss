.blocks-everywhere {
	margin-top: 0.5em;
}

.gutenberg-support #bbpress-forums fieldset.bbp-form {
	.edit-post-visual-editor button,
	.interface-interface-skeleton__header button {
		padding: inherit;
		font-size: inherit;
	}

	.components-dropdown__content button,
	.block-editor-block-switcher__popover button {
		padding: 6px 8px 6px 8px !important;
	}

	fieldset legend {
		font-weight: inherit;
		font-size: inherit;
	}

	.edit-post-visual-editor .block-editor-block-list__block .components-button {
		padding: 6px 12px;
	}

	.blocks-everywhere input {
		padding: 6px 8px;
		margin: 0 8px 0 0;
		outline: inherit;
		border: 1px solid #757575;
		outline-color: inherit;
	}

	.blocks-everywhere input[type="text"] {
		height: inherit;
		min-height: inherit;
		padding: 6px 8px;
		margin: 0px;
		outline: inherit;
		border: 1px solid #757575;
		outline-color: inherit;
	}

	.blocks-everywhere .block-editor-media-placeholder__url-input-form button {
		padding: 0;
	}

	.components-base-control__field label {
		font-size: 11px;
		margin-bottom: calc(4px * 2);
	}

	.block-editor-link-control__setting :last-child {
		margin-bottom: 0;
	}

	.is-opened .components-panel__body-title {
		margin: -16px -16px 5px;
	}

	.components-panel__body-title {
		button {
			font-weight: 500;
			padding: 16px 48px 16px 16px !important;
			font-family: inherit;
		}
	}

	button.is-small {
		font-size: 11px !important;
		height: 24px;
		line-height: 22px;
		padding: 0 8px;
	}

	.components-base-control__help {
		font-size: 12px;
	}

	button.block-editor-inserter__patterns-category {
		appearance: none;
		border: 1px solid transparent !important;
		cursor: pointer;
		background: none;
		text-align: start;
		padding: calc((36px - calc(13px * 1.2) - 2px) / 2) 12px;
		width: 100%;
		display: block;
		margin: 0;
		color: inherit;
		border-radius: 2px;

		&:hover {
			border-color: var(--wp-components-color-accent, var(--wp-admin-theme-color, #007cba)) !important;
		}
	}

	.block-editor-inserter__block-patterns-tabs {
		min-height: 500px;
	}

	.block-editor-inserter__patterns-category-dialog {
		margin-left: -100%;
		width: 100%;
		border: none;
	}

	.block-editor-block-patterns-list__item-title {
		display: block !important;
		text-align: left;
		font-size: 14px;
	}

	.block-editor-block-preview__container {
		display: none;
	}

	.block-editor-inserter__search,
	.block-editor-inserter__quick-inserter-results {
		border: none;
	}

	.components-tab-panel__tabs {
		border: none;
	}

	.block-editor-list-view-tree {
		width: 100%;

		button {
			padding: 0 !important;

			&:hover,
			&:focus {
				box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) #fff;
				background-color: transparent;
				color: #fff;
			}
		}
	}

	.blocks-everywhere .components-search-control__input {
		border: none;
		margin: 0 0 8px;
		padding: 16px 48px 16px 16px;
	}

	.block-editor-inserter__patterns-category-panel {
		margin-left: -100%;
	}

	.block-editor-link-control__field .components-base-control__label {
		margin-bottom: 0;
		margin-right: 16px;
	}
}

.gutenberg-support .blocks-everywhere {
	button:not(.is-small) {
		font-size: 13px !important;
	}

	button:not(:hover):not(:active):not(.block-editor-list-view-block__menu):not(.has-background):not(.block-editor-inserter__toggle):not(.is-primary):not(.is-pressed):not(.block-list-appender__toggle) {
		background-color: none;
		color: #1e1e1e;
	}

	.edit-post-header__settings button {
		padding-right: 0 !important;
	}

	.edit-post-header-toolbar__left button {
		padding: 0 !important;
	}

	.components-dropdown-menu__popover button {
		padding: 12px 10px !important;
	}

	.components-tools-panel .components-dropdown-menu__toggle {
		padding: 0px !important;
	}

	/* Block inserter is too high z-index */
	.components-popover {
		z-index: 100;
	}

	.editor-styles-wrapper {
		background-color: white;
	}

	.editor-styles-wrapper .wp-block code {
		background-color: #f0f0f0 !important;
		padding-left: 5px !important;
		padding-right: 5px !important;
	}

	.editor-styles-wrapper ol.wp-block-list {
		margin-left: 10px;
	}
}

body:not(.gutenberg-support-upload) {
	// Hide this button until https://github.com/WordPress/gutenberg/issues/46326 is fixed
	.block-editor-block-toolbar button[aria-label="Upload external image"] {
		display: none;
	}
}

body:not(.gutenberg-support-upload) {
	.block-editor-media-placeholder .components-placeholder__instructions {
		visibility: hidden;
		position: relative;
	}

	.block-editor-media-placeholder .components-placeholder__instructions:after {
		content: "Add an image with a URL.";
		visibility: visible;
		position: absolute;
		top: 0;
		left: 0;
	}
}

.gutenberg-support #bbpress-forums fieldset.bbp-form .blocks-everywhere {
	// Stops the editor changing width depending on the toolbar
	max-width: 620px;
	min-width: 620px;

	// Clean up the link popup
	.block-editor-link-control input[type="text"] {
		padding: 11px 16px;
		height: 100%;
		border: 1px solid #ddd;
	}

	.block-editor-link-control button {
		padding: 6px 12px;
	}

	.xblock-editor-link-control__search-submit:hover {
		background-color: var(--wp-components-color-accent,var(--wp-admin-theme-color,#007cba));
	}

	.components-button.is-secondary,
	.components-button.is-tertiary {
		background: transparent;
		color: var(--wp-components-color-accent, var(--wp-admin-theme-color, #007cba));
		white-space: nowrap;
	}
}

.entry-content .blocks-everywhere {
	h2 {
		margin: inherit;
	}

	.interface-complementary-area h2 {
		color: #1e1e1e;
		font-size: 13px;
		margin-bottom: 0;
	}
}

#bbcode_buttons {
	display: none;
}

.wp-embed-responsive .wp-has-aspect-ratio iframe {
	bottom: 0;
	height: 100%;
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
	border: none;
}

.wp-block-embed {
	overflow: scroll;
}

.gutenberg-support .blocks-everywhere .components-popover.block-editor-block-list__block-side-inserter-popover {
	z-index: 30;
}

.wporg-support.gutenberg-support .block-editor-block-contextual-toolbar.is-fixed {
	top: 92px;
}

@media (min-height: 800px) and (min-width: 890px) {
	.wporg-support.gutenberg-support .block-editor-block-contextual-toolbar.is-fixed {
		top: 122px;
	}
}
