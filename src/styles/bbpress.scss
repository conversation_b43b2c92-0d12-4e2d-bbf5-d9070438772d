@import "@wordpress/base-styles/_colors";

// bbPress
.gutenberg-support {
	iframe {
		border: 0;
	}

	// No way to stop this HTML appearing through filters
	.form-allowed-tags {
		display: none;
	}

	// Let popovers show outside the editor
	#bbpress-forums,
	.interface-interface-skeleton,
	.interface-interface-skeleton__body,
	.interface-interface-skeleton__content,
	.interface-interface-skeleton__editor {
		overflow: inherit;
	}

	#bbpress-forums,
	.bbp-topic-form {
		/* Override bbPress list styles */
		.editor-styles-wrapper li,
		.editor-styles-wrapper ul {
			list-style: inherit;
			margin: inherit;
			padding: inherit;
			font-size: inherit;
		}

		.editor-styles-wrapper ul {
			padding-left: 15px;
		}

		.editor-styles-wrapper li {
			margin-top: 0;
			padding-left: 5px;
			margin-bottom: 0.5em;
		}

		// Clean up the link popup
		fieldset.bbp-form .block-editor-link-control input[type="text"] {
			padding: 11px 16px;
			height: 100%;
		}
	}

	.block-editor-block-contextual-toolbar.is-fixed {
		// Offset the admin bar
		top: 32px;
		width: 100%;
	}

	.bbp-reply-form fieldset.bbp-form,
	#bbp-new-topic #new-post fieldset.bbp-form,
	#bbpress-forums fieldset.bbp-form {
		.components-tools-panel-item legend {
			font-size: 11px;
			font-weight: 500;
			display: inline-block;
			padding: 0;
		}

		button.has-icon {
			padding: 0;
		}

		.block-editor-block-navigation__container p {
			margin-bottom: 0.5em;
		}

		.block-editor-list-view-tree {
			width: 100%;

			button {
				padding: 0;

				&:hover,
				&:focus {
					box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) #fff;
					background-color: transparent;
					color: #fff;
				}
			}
		}
	}
}

// Loading animation until JS is loaded
body:not(.gutenberg-support-loaded) .blocks-everywhere {
	min-height: 280px;
	background-color: $gray-200;
	border: 1px solid $gray-200;
	animation: loading-fade 1.6s ease-in-out infinite;
	margin-bottom: 57px;
}

body:not(.gutenberg-support-loaded) .iso-editor {
	display: none;
}

/* Reset bbPress code style */
body.page .bbp-topic-form,
body.single-topic .bbp-reply-form,
body.topic-edit .bbp-topic-form,
body.reply-edit .bbp-reply-form,
body.single-forum .bbp-topic-form {
	.editor-styles-wrapper code {
		border: none;
		background-color: inherit;
		font-size: inherit;
		padding: 0;
		margin-top: 0;
		width: inherit;
		display: inline;
	}

	.editor-styles-wrapper pre {
		margin-top: 16px;
		margin-bottom: 16px;
	}
}

@keyframes loading-fade {
	0% {
		opacity: 0.3;
	}
	50% {
		opacity: 1;
	}
	100% {
		opacity: 0.3;
	}
}
