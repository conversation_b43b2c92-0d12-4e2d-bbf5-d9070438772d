@import "~@automattic/typography/styles/variables";
@import "~@automattic/color-studio/dist/color-variables";

.be-support-content-placeholder {
  .components-placeholder__input[type="url"] {
    margin: 0 8px 0 0 !important;
  }

  .components-notice-list .components-notice__content {
    margin: 4px 25px 4px 0;
  }

  &__search-slot {
    flex-basis: 100%;
    position: relative;
  }
}

.be-support-content-confirm-anchor {
  position: relative;
}

.be-support-content-confirm-content {
  padding: 12px;
  background: $studio-white;

  button.is-tertiary {
    padding: 6px 12px;
  }

  &__item {
    font-family: "SF Pro Text", $sans !important;
    font-weight: 400;
    font-size: $font-body-small !important;
    color: $studio-gray-60 !important;

    &:hover {
      box-shadow: inset 0 0 0 1px $studio-gray-60 !important;
    }

    &.is-destructive {
      color: $studio-red-50 !important;

      &:hover {
        box-shadow: inset 0 0 0 1px $studio-red-50 !important;
      }
    }
  }
}

.be-support-content-search-results {
  padding: 10px;
  background-color: $studio-white;

  border: 1px solid #D8DBDD;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border-radius: 2px;

  position: absolute;
  left: 0;
  top: 12px;
  width: 100%;

  z-index: 10000;

  &__loading {
    display: flex;
  }

  &__list {
  }

  &__item {
    padding: 10px 20px;
    cursor: pointer;

    &:hover {
        background-color: $studio-gray-0;
    }
  }

  &__title {
    font-family: "SF Pro Text", $sans;
    font-style: normal;
    font-weight: 500;
    font-size: $font-body;
    line-height: 24px;

    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__link {
    font-family: "SF Pro Text", $sans;
    font-style: normal;
    font-weight: 400;
    font-size: $font-body-small;
    line-height: 20px;

    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}